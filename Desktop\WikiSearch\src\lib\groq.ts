import Groq from 'groq-sdk';

const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY,
});

export async function generateSummary(title: string, content: string): Promise<string> {
  try {
    const prompt = `Please provide a concise, informative summary of the following Wikipedia article about "${title}". 
    Focus on the most important facts and key points. Keep it engaging and easy to understand.
    
    Article content:
    ${content}
    
    Summary:`;

    const completion = await groq.chat.completions.create({
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
      model: 'llama3-8b-8192',
      temperature: 0.3,
      max_tokens: 500,
    });

    return completion.choices[0]?.message?.content || 'Unable to generate summary.';
  } catch (error) {
    console.error('Error generating summary with Groq:', error);
    throw new Error('Failed to generate AI summary');
  }
}
